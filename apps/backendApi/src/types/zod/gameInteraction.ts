// /backendApi/types/zod/gameInteraction.ts

import { z } from 'zod';

// Base game interaction schema
const baseGameInteractionSchema = z.object({
  game_code: z.string().min(1, 'game_code is required'),
  action: z.string().min(1, 'action is required'),
  session_id: z.string().uuid('Invalid session_id format'),
  user_id: z.string().uuid('Invalid user_id format'),
  operator_id: z.string().min(1, 'operator_id is required'),
  currency: z.string().min(3, 'currency must be valid 3-letter code'),
  play_mode: z.enum(['real', 'demo'], {
    errorMap: () => ({ message: 'play_mode must be "real" or "demo"' })
  }),
});

// Mines-specific schemas
export const minesStartRoundSchema = baseGameInteractionSchema.extend({
  game_code: z.literal('ins_mines'),
  action: z.literal('startRound'),
  grid_size: z.number().int().min(3).max(6),
  mine_count: z.number().int().min(1).max(35),
  bet_amount: z.number().positive().optional().default(1),
  // SECURITY: Cryptographic seeds are NOT accepted from frontend
  // server_seed, client_seed, and nonce are generated server-side only
});

export const minesRevealTileSchema = baseGameInteractionSchema.extend({
  game_code: z.literal('ins_mines'),
  action: z.literal('revealTileAction'),
  round_id: z.string().uuid('Invalid round_id format'),
  index: z.number().int().min(0).max(35),
  bet_amount: z.number().positive().optional().default(1),
});

export const minesCashoutSchema = baseGameInteractionSchema.extend({
  game_code: z.literal('ins_mines'),
  action: z.literal('cashout'),
  round_id: z.string().uuid('Invalid round_id format'),
  bet_amount: z.number().positive().optional().default(1),
});

// Lootbox-specific schemas
export const lootboxPlaySchema = baseGameInteractionSchema.extend({
  action: z.literal('play'),
  bet_amount: z.number().positive().optional().default(1),
  client_seed: z.string().optional(),
  nonce: z.number().int().optional(),
  use_fairness: z.boolean().optional().default(false),
});

// Universal game interaction schema that validates based on game_code and action
export const gameInteractionSchema = z.discriminatedUnion('game_code', [
  // Mines game schemas
  minesStartRoundSchema,
  minesRevealTileSchema,
  minesCashoutSchema,
  // Lootbox game schemas
  lootboxPlaySchema.extend({ game_code: z.literal('ins_lootbox') }),
  lootboxPlaySchema.extend({ game_code: z.literal('ins_lootbox_base') }),
  // Add more games as needed
]);

// Type exports
export type GameInteractionPayload = z.infer<typeof gameInteractionSchema>;
export type MinesStartRoundPayload = z.infer<typeof minesStartRoundSchema>;
export type MinesRevealTilePayload = z.infer<typeof minesRevealTileSchema>;
export type MinesCashoutPayload = z.infer<typeof minesCashoutSchema>;
export type LootboxPlayPayload = z.infer<typeof lootboxPlaySchema>;

// Validation helper
export function validateGameInteraction(data: unknown): GameInteractionPayload {
  return gameInteractionSchema.parse(data);
}

// Action validation helper
export function getValidActionsForGame(gameCode: string): string[] {
  switch (gameCode) {
    case 'ins_mines':
      return ['startRound', 'revealTileAction', 'cashout'];
    case 'ins_lootbox':
    case 'ins_lootbox_base':
      return ['play'];
    default:
      return [];
  }
}
